{"containerDefinitions": [{"name": "ekko-dev", "image": "************.dkr.ecr.us-east-1.amazonaws.com/ekko/ekko-backend:latest", "cpu": 0, "portMappings": [{"name": "3002", "containerPort": 3002, "hostPort": 3002, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "PORT", "value": "3002"}, {"name": "AWS_REGION", "value": "us-east-1"}, {"name": "AWS_S3_BUCKET_NAME", "value": "ekkomd-prod-india"}, {"name": "AUTH0_ISSUER_URL", "value": "https://auth-dev.ekkomd.com/"}, {"name": "AUTH0_AUDIENCE", "value": "uma6kG995LqLc50v0zmY2zvSnhXlmy2v"}, {"name": "WHISPER_MODEL_PATH", "value": "https://api.openai.com/v1/audio/transcriptions"}, {"name": "GOOGLE_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "GOOGLE_PROJECT_ID", "value": "ekko-transcription"}, {"name": "GOOGLE_CLIENT_ID", "value": "110054212904492596101"}, {"name": "GOOGLE_BUCKET_NAME", "value": "ekkomd"}, {"name": "AUTH0_DOMAIN", "value": "dev-iqxyolpw0karbhgx.us.auth0.com"}, {"name": "AUTH0_CLIENT_ID", "value": "7b5ZdXc1CFewTOz0ieV4gv0zhk9sqx4t"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:develop/ekkomd-core-service/aws-access-key-0wBNjE:AWS_ACCESS_KEY_ID::"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:develop/ekkomd-core-service/aws-access-key-0wBNjE:AWS_SECRET_ACCESS_KEY::"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:develop/postgres-core-db/rmata-db-url-xQvp8c:DATABASE_URL::"}, {"name": "GOOGLE_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:prod/gcp/private-key-wzPHgz:GCP_PRIVATE_KEY::"}, {"name": "GOOGLE_PRIVATE_KEY_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:prod/gcp/private-key-wzPHgz:GCP_PRIVATE_KEY_ID::"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:develop/openai/api-key-PLrcDR:OPENAI_API_KEY::"}, {"name": "AUTH0_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:develop/ekkomd/auth0_client_secret-YGoizV:AUTH0_CLIENT_SECRET::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ekko-dev-task-def", "awslogs-create-group": "true", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3002/health/ || exit 1"], "interval": 30, "timeout": 5, "retries": 3}, "systemControls": []}], "family": "ekko-dev-task-def", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 5, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.docker-remote-api.1.24"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "ecs.capability.container-health-check"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": []}