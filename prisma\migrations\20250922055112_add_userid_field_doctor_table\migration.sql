/*
  Warnings:

  - A unique constraint covering the columns `[user_id]` on the table `doctor` will be added. If there are existing duplicate values, this will fail.
*/
-- AlterTable
ALTER TABLE "doctor" ADD COLUMN     "user_id" UUID;

-- CreateIndex
CREATE UNIQUE INDEX "doctor_user_id_key" ON "doctor"("user_id");

-- AddForeignKey
ALTER TABLE "doctor" ADD CONSTRAINT "doctor_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "tbl_user_details"("user_id") ON DELETE SET NULL ON UPDATE CASCADE;
