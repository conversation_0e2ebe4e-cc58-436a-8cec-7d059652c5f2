/*
  Warnings:

  - A unique constraint covering the columns `[contact_id]` on the table `doctor` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[role_id,permission_id]` on the table `tbl_role_permission_mapping` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[contact_id]` on the table `tbl_user_details` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "AddressType" AS ENUM ('HOME', 'WORK', 'CLINIC', 'OTHER');

-- AlterTable
ALTER TABLE "doctor" ADD COLUMN     "contact_id" UUID;

-- AlterTable
ALTER TABLE "tbl_user_details" ADD COLUMN     "contact_id" UUID;

-- CreateTable
CREATE TABLE "contact" (
    "contact_id" UUID NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "contact_pkey" PRIMARY KEY ("contact_id")
);

-- CreateTable
CREATE TABLE "address" (
    "address_id" UUID NOT NULL,
    "contact_id" UUID NOT NULL,
    "address_type" "AddressType" NOT NULL,
    "street" TEXT,
    "city" TEXT NOT NULL,
    "zip" TEXT,
    "country" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL DEFAULT 'ADMIN',
    "updated_at" TIMESTAMP(3),
    "updated_by" TEXT,

    CONSTRAINT "address_pkey" PRIMARY KEY ("address_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "contact_email_key" ON "contact"("email");



-- AddForeignKey
ALTER TABLE "doctor" ADD CONSTRAINT "doctor_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contact"("contact_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tbl_user_details" ADD CONSTRAINT "tbl_user_details_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contact"("contact_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "address" ADD CONSTRAINT "address_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contact"("contact_id") ON DELETE RESTRICT ON UPDATE CASCADE;
