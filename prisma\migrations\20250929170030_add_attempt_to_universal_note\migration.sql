/*
  Warnings:

  - A unique constraint covering the columns `[contact_id]` on the table `doctor` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[contact_id]` on the table `tbl_user_details` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "encounter" ADD COLUMN     "context" TEXT;

-- CreateTable
CREATE TABLE "universal_note" (
    "id" TEXT NOT NULL,
    "encounter_id" UUID NOT NULL,
    "response" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "attempt" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "universal_note_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "doctor_contact_id_key" ON "doctor"("contact_id");

-- CreateIndex
CREATE UNIQUE INDEX "tbl_user_details_contact_id_key" ON "tbl_user_details"("contact_id");

-- AddForeignKey
ALTER TABLE "universal_note" ADD CONSTRAINT "universal_note_encounter_id_fkey" FOREIGN KEY ("encounter_id") REFERENCES "encounter"("encounter_id") ON DELETE RESTRICT ON UPDATE CASCADE;
