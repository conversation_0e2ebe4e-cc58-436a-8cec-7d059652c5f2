import { Module } from '@nestjs/common';
import { EncounterModule } from './encounter/encounter.module';
import { TranscribeModule } from './transcribe/transcribe.module';
import { PrismaModule } from './common/prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { DoctorModule } from './doctor/doctor.module';
import { HealthModule } from './common/health/health.module';
import { NotesModule } from './notes/notes.module';
import { PatientModule } from './patient/patient.module';
import { TranscriptionsModule } from './transcriptions/transcriptions.module';
import { LabsModule } from './labs/labs.module';
import { UtilsModule } from './common/utils/utils.module';
import { PrescriptionsModule } from './prescriptions/prescriptions.module';
import { PlanModule } from './plan/plan.module';
import { OnboardingModule } from './onboarding/onboarding.module';
import { SubscriptionModule } from './subscription/subscription.module';
import { LetterheadModule } from './letterhead/letterhead.module';
import { TenantModule } from './tenant/tenant.module';
import { FeedbackModule } from './feedback/feedback.module';
import { UniversalPicklistModule } from './universal-picklist/universal-picklist.module';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import {
  THROTTLE_REQUESTS,
  THROTTLE_TIME,
} from './common/config/common.config';

import { UserrolemappingModule } from './userrolemapping/userrolemapping.module';
import { UserdetailsModule } from './userdetails/userdetails.module';
import { VitalModule } from './vitals/vitals.module';

import { UserPermissionModule } from './user-permission/user-permission.module';
import { RolePermissionModule } from './role-permission/role-permission.module';
import { RolesModule } from './roles/roles.module';
import { PermissionsModule } from './permissions/permissions.module';
import { AccessControlModule } from './common/access-control/access-control.module';
import { EncounterGeneratorModule } from './encounter-generator/encounter-generator.module';
import { SettingsModule } from './settings/settings.module';
import { Auth0Module } from './auth/auth0/auth0.module';
import { UserSettingsModule } from './user-settings/user-settings.module';

@Module({
  imports: [
    PrismaModule,
    EncounterModule,
    TranscribeModule,
    AuthModule,
    DoctorModule,
    HealthModule,
    NotesModule,
    PatientModule,
    TranscriptionsModule,
    LabsModule,
    UtilsModule,
    PrescriptionsModule,
    PlanModule,
    OnboardingModule,
    SubscriptionModule,
    LetterheadModule,
    TenantModule,
    FeedbackModule,
    UserdetailsModule,
    UniversalPicklistModule,
    VitalModule,
    PermissionsModule,
    Auth0Module,
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: THROTTLE_TIME, //1 minute
        limit: THROTTLE_REQUESTS, //100 requests
      },
    ]),
    UserrolemappingModule,
    UserdetailsModule,
    UserPermissionModule,
    RolePermissionModule,
    RolesModule,
    PermissionsModule,
    AccessControlModule,
    EncounterGeneratorModule,
    SettingsModule,
    UserSettingsModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
