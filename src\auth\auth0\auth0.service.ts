import { Injectable, BadRequestException } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class Auth0Service {
  private domain = process.env.AUTH0_DOMAIN;
  private clientId = process.env.AUTH0_CLIENT_ID;
  private clientSecret = process.env.AUTH0_CLIENT_SECRET;
  private audience = `https://${process.env.AUTH0_DOMAIN}/api/v2/`;

  // 1. Get Management API token
  async getManagementToken(): Promise<string> {
    const response = await axios.post(`https://${this.domain}/oauth/token`, {
      client_id: this.clientId,
      client_secret: this.clientSecret,
      audience: this.audience,
      grant_type: 'client_credentials',
    });
    return response.data.access_token;
  }

  // 2. Send invitation email (Auth0 change_password flow)
  async sendInvitationEmail(email: string): Promise<any> {
    const response = await axios.post(
      `https://${this.domain}/dbconnections/change_password`,
      {
        client_id: this.clientId,
        email,
        connection: 'Username-Password-Authentication',
      },
      { headers: { 'Content-Type': 'application/json' } },
    );
    return response.data;
  }

  // 3. Invite user: create if not exists, else throw error
  async inviteUser(email: string): Promise<any> {
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      const token = await this.getManagementToken();

      // Step A: check if user exists
      const usersRes = await axios.get(
        `https://${this.domain}/api/v2/users-by-email`,
        {
          headers: { Authorization: `Bearer ${token}` },
          params: { email },
        },
      );

      const user = usersRes.data[0];

      if (user) {
        // If user exists → stop here
        throw new BadRequestException('User already exists');
      }

      // Step B: create user in Auth0
      const newUserRes = await axios.post(
        `https://${this.domain}/api/v2/users`,
        {
          email: email,
          connection: 'Username-Password-Authentication',
          password: Math.random().toString(36).slice(-10) + 'A1!',
          email_verified: false,
          verify_email: true,
        },
        { headers: { Authorization: `Bearer ${token}` } },
      );

      const newUser = newUserRes.data;

      // Step C: send invitation (password reset) email
      const invitation = await this.sendInvitationEmail(email);

      return {
        type: 'db',
        message: 'User created and signup email sent successfully',
        user: newUser,
        signupLink: invitation,
      };
    } catch (err: any) {
      console.error('Auth0 invite error', err.response?.data || err.message);
      throw new BadRequestException(
        err.response?.data?.message || err.message,
      );
    }
  }
}
