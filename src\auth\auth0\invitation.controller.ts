import { <PERSON>, Post, Body } from '@nestjs/common';
import { Auth0Service } from './auth0.service';

@Controller('invite')
export class InvitationController {
  constructor(private readonly auth0Service: Auth0Service) { }

  @Post()
  async invite(@Body('email') email: string) {
    const result = await this.auth0Service.inviteUser(email);
    return { message: 'Invitation email sent via Auth0', result };
  }
}
