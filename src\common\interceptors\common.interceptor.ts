import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiResponseDTO, PaginationResDTO } from '../dtos';
import { UserSettingsService } from 'src/user-settings/user-settings.service';
import { AiConfig } from '../config/common.config';
import { ignoredRoutes } from '../constants/common.constants';

@Injectable()
export class CommonInterceptor implements NestInterceptor {
  constructor(private readonly usersettingservice: UserSettingsService) {}
  async intercept(context: ExecutionContext, next: CallHandler): Promise<any> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const user = request.user;
    if (!user) {
      return next.handle();
    }

    const settings = await this.usersettingservice.getUserSetting(
      user.email,
      null,
    );
    request.user.settings = settings.setting_value.setting.reduce(
      (acc, item) => {
        acc[item.key] = item.value;
        return acc;
      },
      {},
    );
    const aiConfig = new AiConfig();
    aiConfig.setDefaultModel(
      request.user.settings['default.ai.model'] ?? 'gpt-4o',
    );

    return next.handle();
  }
}
