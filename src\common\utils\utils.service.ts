import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import axios from 'axios';
import { LLM_RESPONSETYPES } from '../constants/common.constants';
import { PaginationResDTO } from '../dtos/PaginationRes.dto';
import * as FormData from 'form-data';
import { SpeechClient } from '@google-cloud/speech';
import { google } from '@google-cloud/speech/build/protos/protos';
import type { ClientOptions } from 'google-gax';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { SpeechClient as SpeechClientV2 } from '@google-cloud/speech/build/src/v2';
import { Storage, StorageOptions } from '@google-cloud/storage';
import { extname } from 'path';
import { exec } from 'child_process';
import { AiConfig } from '../config/common.config';

@Injectable()
export class UtilsService {
  private readonly logger = new Logger(UtilsService.name);
  constructor(private readonly prismaservice: PrismaService) {}

  /**
   * Standardized error handler for consistent error responses across the application
   * @param error The caught error
   * @param userMessage A user-friendly message
   * @param statusCode HTTP status code to return
   * @returns HttpException with standardized format
   */
  formatErrorResponse(
    error: any,
    userMessage: string = 'An unexpected error occurred',
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
  ): HttpException {
    this.logger.error(`Error: ${userMessage}`, error?.stack || error);

    return new HttpException(
      {
        message: userMessage,
        error: error?.stack || JSON.stringify(error) || error,
        data: null,
      },
      statusCode,
    );
  }

  IsParsableJson(json: string): boolean {
    try {
      JSON.parse(json);
      return true;
    } catch (error) {
      this.logger.error('Error parsing JSON:', error);
      return false;
    }
  }

  async genereteResponseFromLLM(
    model: string = 'gpt-4o',
    role: string = 'user',
    prompt: string,
    max_tokens: number = 1200,
    temperature: number = 0.7,
    expected_type: LLM_RESPONSETYPES = LLM_RESPONSETYPES.TEXT,
    response_type: string,
    transcription_ids: string[],
    userEamil: string,
  ): Promise<any> {
    try {
      model = AiConfig.DEFAULT_AI_MODEL;

      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: model,
          messages: [
            {
              role: role,
              content: prompt,
            },
          ],
          max_tokens: max_tokens,
          temperature: temperature,
        },
        {
          headers: {
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
          },
        },
      );
      this.logger.log('Response generated successfully.');
      const rawresponse = response.data.choices[0].message.content;
      this.logger.debug(
        'First attempt response:---------------------------------',
        rawresponse,
      );
      //check the responsetype exists or not
      const responsetypedetails =
        await this.prismaservice.responseType.findFirst({
          where: {
            isActive: true,
            type_name: {
              equals: response_type,
            },
          },
          select: {
            label: true,
          },
        });

      if (!responsetypedetails) {
        this.logger.error(`Response type not found for type: ${response_type}`);
        throw new NotFoundException('Response type not found');
      }

      //insert the raw response to db
      const rawresponsedetails = await this.prismaservice.responseLLM.create({
        data: {
          raw_response: rawresponse,
          response_type: response_type,
          created_by: userEamil,
        },
        select: {
          responseId: true,
        },
      });

      if (!rawresponsedetails) {
        this.logger.error('Failed to insert raw LLM response to database');
        throw this.formatErrorResponse(
          new Error('Failed to insert raw response'),
          'Failed to save response data',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      //insert to mapping table
      await this.prismaservice.audioTranscription_LLMResponse.createMany({
        data: transcription_ids.map((id) => ({
          response_id: rawresponsedetails.responseId,
          transcription_id: id,
          created_by: userEamil,
        })),
      });

      if (expected_type == LLM_RESPONSETYPES.JSON) {
        if (!this.IsParsableJson(rawresponse)) {
          this.logger.log(
            'Response generated successfully but not parsable as JSON, attempting to refine...',
          );
          const refinedjson = await axios.post(
            'https://api.openai.com/v1/chat/completions',
            {
              model: model,
              messages: [
                {
                  role: 'user',
                  content: `The provided output is not in valid JSON format and cannot be parsed properly. Please review and correct it to ensure it is in pure JSON format. Ensure there are no markups, special characters, or formatting errors, and that the output can be parsed directly as valid JSON. Return only the corrected JSON in pure text format. If you find \`\`\`json\`\`\` remove this type of markup and give response in pure text Output: ${rawresponse}`,
                },
              ],
              max_tokens: 1200,
              temperature: 0.6,
            },
            {
              headers: {
                Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
              },
            },
          );
          const refined_rawresponse =
            refinedjson.data.choices[0].message.content;
          this.logger.debug(
            'Second attempt response:-----------------------------------',
            refined_rawresponse,
          );
          if (this.IsParsableJson(refined_rawresponse)) {
            this.logger.log(
              'Response generated successfully and able to parse on second attempt.',
            );

            await this.prismaservice.responseLLM.update({
              where: {
                isActive: true,
                responseId: rawresponsedetails.responseId,
              },
              data: {
                raw_response: refined_rawresponse,
                response_type: response_type,
                updated_by: userEamil,
                successful: true,
              },
            });
            return refined_rawresponse;
          } else {
            this.logger.error(
              `Failed to generate valid JSON for ${responsetypedetails.label} even after refinement attempt`,
            );
            throw this.formatErrorResponse(
              new Error('Invalid JSON response after refinement'),
              `Oops! Something went wrong while trying to create the ${responsetypedetails.label} section`,
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        } else {
          //if the json is parsable success status will be true
          await this.prismaservice.responseLLM.update({
            where: {
              isActive: true,
              responseId: rawresponsedetails.responseId,
            },
            data: {
              updated_by: userEamil,
              successful: true,
            },
          });
        }
      }

      return rawresponse;
    } catch (error) {
      this.logger.error(
        `Error generating LLM response with model ${model}:`,
        error.message,
      );
      if (error instanceof HttpException) {
        throw error; // If it's already our formatted error, just rethrow
      }
      throw this.formatErrorResponse(
        error,
        `Failed generating the response: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  getPagination(
    page: number,
    limit: number,
    totalRecords: number,
  ): PaginationResDTO {
    const totalPages = Math.ceil(totalRecords / limit);

    const pagination: PaginationResDTO = {
      currentPage: page,
      recordsPerPage: limit,
      totalRecords,
      totalPages,
    };
    return pagination;
  }

  /**
   * Converts audio buffer to WAV format and returns the result as a buffer
   * @param filename Original filename (used for temp file naming)
   * @param contentType Original content type of the audio
   * @param audioBuffer Buffer containing the audio data
   * @returns Promise with the converted WAV buffer and its content type
   */
  async convertToWav(
    filename: string,
    audioBuffer: Buffer,
    sampleRateHertz: number = 48000,
  ): Promise<{ contentType: string; audioBuffer: Buffer }> {
    // Create temporary directory if it doesn't exist
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Generate unique file names for input and output
    const fileBaseName = path.parse(filename).name.replaceAll(' ', '_');
    const timestamp = Date.now();
    const tempInputPath = path.join(
      tempDir,
      `${fileBaseName}_${timestamp}_input${path.extname(filename)}`,
    );
    const tempOutputPath = path.join(
      tempDir,
      `${fileBaseName}_${timestamp}_output.wav`,
    );

    try {
      // Write buffer to temporary input file
      await promisify(fs.writeFile)(tempInputPath, audioBuffer);

      // Convert to WAV using fluent-ffmpeg
      await new Promise<void>((resolve, reject) => {
        ffmpeg(tempInputPath)
          .outputFormat('wav')
          .audioFrequency(sampleRateHertz)
          // Optional: you can also set other audio parameters if needed
          // .audioChannels(1)  // Mono audio
          // .audioBitrate('128k')  // Bitrate
          .output(tempOutputPath)
          .on('end', () => {
            this.logger.log(`Successfully converted audio to WAV format`);
            resolve();
          })
          .on('error', (err) => {
            this.logger.error(`Error converting file: ${err.message}`);
            reject(new Error(`Conversion failed: ${err.message}`));
          })
          .run();
      });

      // Read the output WAV file into a buffer
      const wavBuffer = await promisify(fs.readFile)(tempOutputPath);

      // Return the WAV buffer and its content type
      return {
        contentType: 'audio/wav',
        audioBuffer: wavBuffer,
      };
    } catch (error) {
      this.logger.error(`Failed to convert audio: ${error.stack}`);
      throw error;
    } finally {
      // Clean up temporary files
      try {
        if (fs.existsSync(tempInputPath)) {
          fs.unlinkSync(tempInputPath);
        }
        if (fs.existsSync(tempOutputPath)) {
          fs.unlinkSync(tempOutputPath);
        }
      } catch (err) {
        this.logger.warn(`Failed to delete temporary files: ${err.message}`);
      }
    }
  }

  separateCharactersAndNumbers(input: string): {
    characters: string;
    numbers: string;
  } {
    let characters = '';
    let numbers = '';

    for (let i = 0; i < input.length; i++) {
      const char = input[i];

      if (/\d/.test(char)) {
        // If it's a digit, add to numbers
        numbers += char;
      } else if (/[a-zA-Z]/.test(char)) {
        // If it's a letter, add to characters
        characters += char;
      }
      // Skip special characters and spaces
    }

    return {
      characters,
      numbers,
    };
  }
}

@Injectable()
export class WhisperService {
  private readonly logger = new Logger(WhisperService.name);
  private readonly whisperApiEndpoint: string;
  private readonly whisperApiKey: string;
  constructor(private readonly utilsService: UtilsService) {
    if (!process.env.OPENAI_API_KEY || !process.env.WHISPER_MODEL_PATH) {
      this.logger.error(
        'Whisper API requires OPENAI_API_KEY and WHISPER_MODEL_PATH environment variables',
      );
      throw new Error(
        'Whisper API requires OPENAI_API_KEY and WHISPER_MODEL_PATH environment variables',
      );
    }

    this.whisperApiEndpoint = process.env.WHISPER_MODEL_PATH;
    this.whisperApiKey = process.env.OPENAI_API_KEY;
  }

  async recognizeSpeechByWhisper(
    filename: string,
    contentType: string,
    audioBuffer: Buffer<ArrayBufferLike>,
    prompt: string,
  ): Promise<{ transcription: string; detectedLanguage: string }> {
    const formData = new FormData();
    formData.append('file', audioBuffer, {
      filename,
      contentType: contentType,
    });
    formData.append('model', 'whisper-1');
    formData.append('response_format', 'json');
    formData.append('temperature', '0');
    formData.append('prompt', prompt);

    let whisperResponse;
    try {
      whisperResponse = await axios.post(this.whisperApiEndpoint, formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${this.whisperApiKey}`,
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });
      this.logger.debug('Whisper API Response:', whisperResponse.data);

      const raw_transcription = whisperResponse.data.text || '';

      if (!raw_transcription) {
        this.logger.error('Empty transcription received from Whisper API');
        throw new Error('No transcription received from Whisper API');
      }

      return {
        transcription: raw_transcription,
        detectedLanguage: whisperResponse.data.language || '',
      };
    } catch (error) {
      this.logger.error(
        `Error generating transcription for audio file: ${filename || 'unknown'}. Error: ${error.message}`,
        error?.stack,
      );
      throw new Error('we failed to generate the transcript');
    }
  }
}

@Injectable()
export class GoogleSpeechService implements OnModuleInit {
  private readonly logger = new Logger(GoogleSpeechService.name);
  private speechClient: SpeechClient;
  private speechClientV2: SpeechClientV2;
  private gcpStorage: Storage;
  private readonly gcpPrivateKey: string;

  async onModuleInit() {
    const bin = process.env.FFMPEG_PATH ?? 'ffmpeg';
    const probe = process.env.FFPROBE_PATH ?? 'ffprobe';

    ffmpeg.setFfmpegPath(bin);
    ffmpeg.setFfprobePath(probe);
    this.logger.log(`Using FFMPEG @ ${bin}`);
    this.logger.log(`Using FFPROBE @ ${probe}`);

    try {
      const execAsync = promisify(exec);
      const { stdout } = await execAsync('ffmpeg -version');
      this.logger.log('FFmpeg is installed successfully');
      this.logger.debug(`FFmpeg version info: ${stdout.split('\n')[0]}`);
    } catch (error) {
      this.logger.error(
        'FFmpeg is not installed or not in PATH',
        error.message,
      );
    }
  }

  constructor(private readonly utilsService: UtilsService) {
    const rawKey = process.env.GOOGLE_PRIVATE_KEY ?? '';
    if (!rawKey) throw new Error('GOOGLE_PRIVATE_KEY not set');
    this.gcpPrivateKey = rawKey.replace(/\\n/g, '\n'); // Necessary when retrieving PEM keys from secretsManager

    try {
      const google_client_configv1: ClientOptions = {
        credentials: {
          client_email: process.env.GOOGLE_CLIENT_EMAIL,
          private_key: this.gcpPrivateKey,
          project_id: process.env.GOOGLE_PROJECT_ID,
          client_id: process.env.GOOGLE_CLIENT_ID,
        },
      };
      const google_client_configv2: ClientOptions = {
        credentials: {
          client_email: process.env.GOOGLE_CLIENT_EMAIL,
          private_key: this.gcpPrivateKey,
          project_id: process.env.GOOGLE_PROJECT_ID,
          client_id: process.env.GOOGLE_CLIENT_ID,
        },
        apiEndpoint: 'asia-southeast1-speech.googleapis.com',
      };
      const storageOptions: StorageOptions = {
        projectId: process.env.GOOGLE_PROJECT_ID,
        credentials: {
          client_email: process.env.GOOGLE_CLIENT_EMAIL,
          private_key: this.gcpPrivateKey,
          project_id: process.env.GOOGLE_PROJECT_ID,
          client_id: process.env.GOOGLE_CLIENT_ID,
        },
      };
      // Initialize the Google Cloud Speech client
      this.gcpStorage = new Storage(storageOptions);
      this.speechClient = new SpeechClient(google_client_configv1);
      this.speechClientV2 = new SpeechClientV2(google_client_configv2);
    } catch (error) {
      this.logger.error(
        'Failed to initialize Google Speech client',
        error?.stack,
      );
      throw new Error('Failed to initialize Google Speech client');
    }
  }

  async recognizeSpeechByGoogle(
    filename: string,
    contentType: string,
    audioBuffer: Buffer,
  ): Promise<{ transcription: string; detectedLanguage: string }> {
    try {
      // Configure the request

      const wav = await this.utilsService.convertToWav(filename, audioBuffer);
      const audio = {
        content: wav.audioBuffer.toString('base64'),
      };

      let detectedLanguage;
      let transcription;

      // Configure the recognition settings
      const config: google.cloud.speech.v1.IRecognitionConfig = {
        encoding: this.determineEncoding(contentType),
        // sampleRateHertz: 16000, // You might want to make this configurable
        languageCode: 'en-US', // You might want to make this configurable
        alternativeLanguageCodes: ['ta-IN', 'en-IN'],
        model: 'latest_long',
        enableAutomaticPunctuation: true,
        useEnhanced: true,
        enableWordTimeOffsets: false,
        speechContexts: [
          {
            phrases: ['doctor', 'patient', 'medical', 'health', 'tamil'],
          },
        ],
      };

      const request: google.cloud.speech.v1.IRecognizeRequest = {
        audio: audio,
        config: config,
      };

      const isLongAudio = wav.audioBuffer.length > 10 * 1024 * 1024; // Check if larger than 10MB

      this.logger.log(`Is long audio: ${isLongAudio}`);

      if (isLongAudio) {
        // For long audio files, use long running recognition
        const [operation] = await this.speechClient.longRunningRecognize({
          config: config,
          audio: audio,
        });

        // Wait for the operation to complete
        const [response] = await operation.promise();
        this.logger.debug('Google Speech API LongRunning Response:', response);

        // Process the long running response
        if (response.results && response.results.length > 0) {
          transcription = response.results
            .map((result) => result.alternatives[0].transcript)
            .join(' ');

          // Get the detected language from the first result
          detectedLanguage = response.results[0]?.languageCode || '';
        }
      } else {
        // Make the API call
        const [response] = await this.speechClient.recognize(request);
        this.logger.debug('Google Speech API Response:', response);

        // Process the response
        transcription = response.results
          .map((result) => result.alternatives[0].transcript)
          .join(' ');

        this.logger.log(`Transcription: ${transcription}`);
      }
      if (!transcription) {
        this.logger.error(
          'Empty transcription received from Google Speech API',
        );
        throw new Error('No transcription received from Google Speech API');
      }

      // Return in a format similar to Whisper for consistency
      return {
        transcription: transcription,
        detectedLanguage: detectedLanguage || '',
      };
    } catch (error) {
      this.logger.error(
        `Error generating transcription for audio file: ${filename || 'unknown'}. Error: ${error.message}`,
        error?.stack,
      );
      throw new Error('Failed to generate the transcript');
    }
  }

  // Helper method to determine the appropriate encoding based on content type
  private determineEncoding(
    contentType: string,
  ):
    | google.cloud.speech.v1.RecognitionConfig.AudioEncoding
    | keyof typeof google.cloud.speech.v1.RecognitionConfig.AudioEncoding
    | null {
    switch (contentType.toLowerCase()) {
      case 'audio/wav':
      case 'audio/wave':
      case 'audio/x-wav':
        return 'LINEAR16';
      case 'audio/ogg':
        return 'OGG_OPUS';
      case 'audio/mpeg':
      case 'audio/mp3':
        return 'MP3';
      case 'audio/flac':
        return 'FLAC';
      case 'audio/webm':
      case 'video/webm':
        return 'WEBM_OPUS';
      // For MP4 containers, we need to be careful as they could contain different codecs
      // Google Cloud typically expects AAC for MP4 audio
      // return 'MP4'; // Note: Check if this is supported directly, you might need to convert
      default:
        this.logger.warn(
          `Unrecognized content type: ${contentType}. Using default encoding.`,
        );
        return 'ENCODING_UNSPECIFIED';
    }
  }

  async googleChirp2Transcription(
    gcsUri: string,
    languageCodes: string[] = ['ta-IN', 'en-IN', 'en-US'],
  ): Promise<{ transcription: string; detectedLanguage: string }> {
    try {
      this.logger.log(`Starting Chirp 2 transcription for file: ${gcsUri}`);

      // Create the configuration for the Chirp 2 model
      const config: google.cloud.speech.v2.IRecognitionConfig = {
        autoDecodingConfig: {}, // Use auto detection for encoding
        languageCodes: languageCodes, // Support multiple languages
        model: 'chirp_2', // Use the advanced Chirp 2 model
      };

      // Define the file metadata (pointing to GCS)
      const fileMetadata = {
        uri: gcsUri, // GCS URI format: gs://[BUCKET]/[FILE]
      };

      // Prepare the recognition request
      const request: google.cloud.speech.v2.IBatchRecognizeRequest = {
        recognizer: `projects/${process.env.GOOGLE_PROJECT_ID}/locations/asia-southeast1/recognizers/_`,
        config: config,
        files: [fileMetadata],
        recognitionOutputConfig: {
          inlineResponseConfig: {}, // Return results inline
        },
      };

      // Start the batch recognition operation
      this.logger.log(
        'Sending batch recognize request to Google Speech API...',
      );
      const [operation] = await this.speechClientV2.batchRecognize(request);

      // Wait for the operation to complete
      this.logger.log('Waiting for operation to complete...');
      const [response] = await operation.promise();

      this.logger.debug('Google Speech API Chirp 2 Response:', response);

      // Process the results
      // The response structure in v2 API is different from v1
      if (
        !response.results ||
        !response.results[gcsUri] ||
        !response.results[gcsUri].transcript
      ) {
        throw new Error('No transcription results received from Chirp 2');
      }

      // Extract transcript from results
      let transcription = '';
      let detectedLanguage = '';

      // Combine all transcript results
      const transcriptResults = response.results[gcsUri].transcript.results;
      if (transcriptResults && transcriptResults.length > 0) {
        transcription = transcriptResults
          .map((result) => result.alternatives[0].transcript)
          .join(' ');

        console.log('final-transcription', transcription);
        // Try to get the language from the first result
        if (transcriptResults[0].languageCode) {
          detectedLanguage = transcriptResults[0].languageCode;
        } else {
          // If language is not explicitly returned, use the first requested language
          detectedLanguage = languageCodes[0];
        }
      }

      this.logger.log(
        `Chirp 2 Transcription complete. Length: ${transcription.length} chars`,
      );

      if (!transcription) {
        this.logger.error('Empty transcription received from Chirp 2');
        throw new Error(
          'No transcription received from Google Speech API Chirp 2',
        );
      }

      return {
        transcription,
        detectedLanguage,
      };
    } catch (error) {
      this.logger.error(
        `Error in googleChirp2Transcription for file: ${gcsUri || 'unknown'}. Error: ${error.message}`,
        error?.stack,
      );
      throw new Error(
        `Failed to generate transcript with Chirp 2: ${error.message}`,
      );
    }
  }
  async uploadAndTranscribeWithChirp2(
    filename: string,
    contentType: string,
    audioBuffer: Buffer,
    bucketName: string,
  ): Promise<{ transcription: string; detectedLanguage: string }> {
    try {
      // We'll assume you have a method to upload to GCS
      // This would typically be handled by the @google-cloud/storage package
      const gcsFilename = `uploads/${new Date().toISOString()}-${filename}`;
      const gcsUri = await this.uploadToGCS(
        bucketName,
        gcsFilename,
        audioBuffer,
        contentType,
      );

      // Now that the file is in GCS, transcribe it with Chirp 2
      return await this.googleChirp2Transcription(gcsUri);
    } catch (error) {
      this.logger.error(
        `Error in uploadAndTranscribeWithChirp2 for file: ${filename}. Error: ${error.message}`,
        error?.stack,
      );
      this.utilsService.formatErrorResponse(
        error,
        'Failed to upload and transcribe with Chirp 2',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async uploadToGCS(
    bucketName: string,
    filename: string,
    buffer: Buffer,
    contentType: string,
  ): Promise<string> {
    try {
      const wav = await this.utilsService.convertToWav(filename, buffer);

      filename =
        new Date().toISOString() +
        '-' +
        filename.replace(extname(filename), '.wav');
      const bucket = this.gcpStorage.bucket(bucketName);
      const file = bucket.file(filename);

      await file.save(wav.audioBuffer, {
        contentType: wav.contentType,
        metadata: { contentType: wav.contentType },
      });

      this.logger.log(`File uploaded to GCS: ${filename}`);

      // Return the GCS URI
      return `gs://${bucketName}/${filename}`;
    } catch (error) {
      this.logger.error(
        `Error uploading to GCS: ${error.message}`,
        error?.stack,
      );
      throw new Error('Failed to upload to Google Cloud Storage');
    }
  }
}
