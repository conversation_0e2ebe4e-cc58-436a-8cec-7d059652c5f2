import { <PERSON>, Get, Param, ParseUUI<PERSON>ipe } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { EncounterGeneratorService } from './encounter-generator.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
@ApiTags('Encounter Generator')
@ApiBearerAuth('access-token')
@Controller('encounter-generator')
export class EncounterGeneratorController {
  constructor(
    private readonly encounterGeneratorService: EncounterGeneratorService,
  ) {}

  @Get(':encounter_id')
  async generateEncounter(
    @Param('encounter_id', ParseUUIDPipe) encounter_id: string,
    @GetUserEmail() userEmail: string,  // ✅ replaced Req with custom decorator
  ) {
    return await this.encounterGeneratorService.generateEncounterDataUnified(
      encounter_id,
      userEmail,
    );
  }

  @Get(':encounter_id/status')
async getEncounterStatus(@Param('encounter_id', ParseUUIDPipe) encounter_id: string) {
  return await this.encounterGeneratorService.getEncounterStatus(encounter_id);
}


}
