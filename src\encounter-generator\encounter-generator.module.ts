import { Module } from '@nestjs/common';
import { EncounterGeneratorController } from './encounter-generator.controller';
import { EncounterGeneratorService } from './encounter-generator.service';
import { TranscriptionsModule } from 'src/transcriptions/transcriptions.module';
import { PrescriptionsModule } from 'src/prescriptions/prescriptions.module';
import { LabsModule } from 'src/labs/labs.module';
import { VitalModule } from 'src/vitals/vitals.module';
import { EncounterGeneratorRepository } from './encounter-generator.repository';
import { PrismaService } from 'src/common/prisma/prisma.service';


@Module({
  imports: [
    TranscriptionsModule,   // ✅ Import this module to access TranscriptionsRepository
    PrescriptionsModule,
    LabsModule,
    VitalModule,
  ],
  controllers: [EncounterGeneratorController],
  providers: [ EncounterGeneratorService,
    EncounterGeneratorRepository, // ✅ add repository here
    PrismaService,]               // ✅ add PrismaService if not globally provided],  // ✅ Only declare providers that don't come from other modules
})
export class EncounterGeneratorModule {}
