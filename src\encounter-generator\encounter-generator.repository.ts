import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';

@Injectable()
export class EncounterGeneratorRepository {
  constructor(private readonly prisma: PrismaService) {}


   async createPendingUniversalNote(encounter_id: string) {
    return this.prisma.universalNote.create({
      data: {
        encounter_id,
        status: 'PENDING',
        attempt: 0,
      },
    });
  }

   async markUniversalNoteCompleted(id: string, notes: string[]) {
    return this.prisma.universalNote.update({
      where: { id },
      data: {
        status: 'COMPLETED',
        response: notes.join('\n'),
      },
    });
  }

  async markUniversalNoteFailed(id: string, currentAttempt: number) {
    const nextAttempt = currentAttempt + 1;
    const newStatus = nextAttempt >= 5 ? 'FAILED' : 'PENDING';

    return this.prisma.universalNote.update({
      where: { id },
      data: {
        status: newStatus,
        attempt: nextAttempt,
      },
    });
  }

  async getStatus(encounter_id: string) {
    return this.prisma.universalNote.findFirst({
      where: { encounter_id },
      orderBy: { created_at: 'desc' },
    });
  }

  // Store context in the Encounter table
  async updateEncounterContext(encounter_id: string, context: string) {
    return await this.prisma.encounter.update({
      where: { encounter_id },
      data: { context },
    });
  }

  // Create multiple UniversalNotes linked to an encounter
  async createUniversalNotes(encounter_id: string, notes: string[], status = 'PENDING') {
    if (!notes || notes.length === 0) return [];

    const data = notes.map(note => ({
      encounter_id,
      response: note,
      status,
    }));

    return await this.prisma.universalNote.createMany({
      data,
      skipDuplicates: true, // optional
    });
  }

  // Optional: fetch universal notes for an encounter
  async getUniversalNotes(encounter_id: string) {
    return await this.prisma.universalNote.findMany({
      where: { encounter_id },
    });
  }
}
