import { Injectable, HttpStatus, NotFoundException, Logger } from '@nestjs/common';
import { TranscriptionsRepository } from 'src/transcriptions/transcriptions.repository';
import { UtilsService } from 'src/common/utils/utils.service';
import { LLM_RESPONSETYPES, TRANSCRIPTION_TYPES } from 'src/common/constants/common.constants';
import { PrescriptionsRepository } from 'src/prescriptions/prescriptions.repository';
import { LabsRepository } from 'src/labs/labs.repository';
import { VitalRepository } from 'src/vitals/vitals.repository';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { EncounterGeneratorRepository } from './encounter-generator.repository';

@Injectable()
export class EncounterGeneratorService {
  private readonly logger = new Logger(EncounterGeneratorService.name);

  constructor(
    private readonly transcriptionsRepository: TranscriptionsRepository,
    private readonly utilsService: UtilsService,
    private readonly prescriptionsRepository: PrescriptionsRepository,
    private readonly labsRepository: LabsRepository,
    private readonly VitalRepository: VitalRepository,
    private readonly prismaservice: PrismaService,
    private readonly encounterGeneratorRepository: EncounterGeneratorRepository,
  ) {}

  async generateEncounterDataUnified(encounter_id: string, userEmail: string) {
  // Step 1: create pending universal note entry
  const pendingNote = await this.encounterGeneratorRepository.createPendingUniversalNote(encounter_id);

  try {
    // Get transcription
    const transcription = await this.transcriptionsRepository.getTranscriptionUsingEncounterId(encounter_id);
    if (!transcription || transcription.length === 0) {
      throw new NotFoundException('No transcription found for this encounter');
    }

    const combinedTranscription = transcription
      .filter(tr => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
      .map(tr => tr.transcription)
      .filter(t => t)
      .join(' ');

    const transcriptionIds = transcription
      .filter(tr => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
      .map(tr => tr.id);

    const prompt = this.buildUnifiedPrompt(combinedTranscription);

    const rawResponse = await this.utilsService.genereteResponseFromLLM(
      'gpt-4o',
      'user',
      prompt,
      2000,
      0.7,
      LLM_RESPONSETYPES.JSON,
      'ENCOUNTER_DATA',
      transcriptionIds,
      userEmail,
    );

    let parsedJson = JSON.parse(rawResponse);

    const prescriptions = parsedJson['Prescriptions'] || [];
    const labs = parsedJson['Labs'] || [];
    const suggestedLabs = parsedJson['Suggested Labs'] || [];
    const vitals = parsedJson['Vitals'] || [];
    const universalNotes = parsedJson['Universal_Notes'] || [];
    const contextSummary = parsedJson['Context_Summary'] || '';

    // Update context + universal notes
    await this.encounterGeneratorRepository.updateEncounterContext(encounter_id, contextSummary);
    await this.encounterGeneratorRepository.createUniversalNotes(encounter_id, universalNotes);

    // ✅ Mark universal note as completed
    await this.encounterGeneratorRepository.markUniversalNoteCompleted(pendingNote.id, universalNotes);

    // Store prescriptions
    const prescriptionsToCreate = prescriptions.map(item => ({
      drug_name: item['Drug Name'],
      dose: item['Dose'],
      sig: item['SIG'],
      brand: item['Brand'],
      patient_notes: item['Patient Notes'],
      encounter_id,
      status: 'PENDING',
      created_by: userEmail,
    }));

    const encounterExists = await this.prismaservice.encounter.findUnique({
      where: { encounter_id },
    });

    if (!encounterExists) {
      throw new Error('Encounter does not exist!');
    }

    const createdPrescriptions = await this.prescriptionsRepository.createPrescriptions({
      info: prescriptionsToCreate,
    }, userEmail);

    // Store labs
    const labsToCreate = labs.map(item => ({
      lab_name: item['Lab Name'],
      notes: item['Notes'],
      dateOrdered: item['Date Ordered'],
      encounter_id,
      status: 'PENDING',
      created_by: userEmail,
    }));
    const createdLabs = await this.labsRepository.createLab({ info: labsToCreate }, userEmail);

    // Suggested labs
    const suggestedLabsToCreate = suggestedLabs.map(item => ({
      lab_name: item['Lab Name'],
      notes: item['Notes'],
      reason: item['Reason'],
      dateOrdered: new Date().toISOString(),
      encounter_id,
      status: 'PENDING',
      created_by: userEmail,
    }));
    const createdSuggestedLabs = await this.labsRepository.createLab({ info: suggestedLabsToCreate }, userEmail);

    // Vitals
    const allVitals = await this.VitalRepository.getAllVitalsTypes();

    function normalize(str: string) {
      return str.toLowerCase().replace(/[^a-z0-9 ]/g, '').trim();
    }

    function findMatchingVital(vitalName: string) {
      const normName = normalize(vitalName);
      return allVitals.find(v => normalize(v.display_name) === normName) ||
             allVitals.find(v => normalize(v.display_name).includes(normName)) ||
             null;
    }

    const vitalsToCreate = vitals.map(item => {
      const matchedVital = findMatchingVital(item['Vital Name']);
      if (!matchedVital) {
        this.logger.warn(`No matching vital found for ${item['Vital Name']}`);
        return null;
      }
      return {
        vital_type_id: matchedVital.vital_type_id,
        encounter_id,
        vital_value: item['Value'],
        measurement_time: transcription[0].created_at.toISOString(),
        created_by: userEmail,
      };
    }).filter(v => v !== null);

    const createdVitals = await this.VitalRepository.createVitals({ info: vitalsToCreate }, userEmail);

    return {
      encounter_id,
      prescriptions: createdPrescriptions,
      labs: createdLabs,
      suggestedLabs: createdSuggestedLabs,
      vitals: createdVitals,
    };

  } catch (error) {
    // ❌ mark universal note failed
    if (pendingNote) {
      await this.encounterGeneratorRepository.markUniversalNoteFailed(pendingNote.id, pendingNote.attempt);
    }
    this.logger.error(`Error generating unified encounter data for ID ${encounter_id}:`, error.stack);
    throw this.utilsService.formatErrorResponse(
      error,
      'Failed to generate encounter data',
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}

// New service method for status
async getEncounterStatus(encounter_id: string) {
  const note = await this.encounterGeneratorRepository.getStatus(encounter_id);
  if (!note) return { status: 'NOT_FOUND' };
  return { status: note.status, attempt: note.attempt };
}


   
      

// private buildUnifiedPrompt(combinedTranscription: string): string {
//   return `
// Based on the provided transcript, extract all medical information as structured JSON. 

// 1. Prescriptions:
// - Extract all prescriptions with these columns: Drug Name, Dose, SIG, Brand, and Patient Notes.
// - Fill missing details with "-".
// - Mention Indian brands where applicable.

// 2. Labs:
// - Extract explicitly ordered labs (Lab Name, Notes, Date Ordered) and suggested labs (Lab Name, Notes, Reason).

// 3. Vitals:
// - Extract only vitals mentioned in the transcript with closest matching names.
// - Include Measurement Time if mentioned; else "-".

// **Important Instructions:**
// - Return a single JSON object with exactly these fields: Prescriptions, Labs, Suggested Labs, Vitals.
// - Do not include Context Memory or Universal Notes.
// - Do not include markdown, backticks, or explanations—return pure JSON only.
// - Do not omit any items present in the transcript.

// Format:
// {
//   "Prescriptions": [ { "Drug Name": "", "Dose": "", "SIG": "", "Brand": "", "Patient Notes": "" } ],
//   "Labs": [ { "Lab Name": "", "Notes": "", "Date Ordered": "" } ],
//   "Suggested Labs": [ { "Lab Name": "", "Notes": "", "Reason": "" } ],
//   "Vitals": [ { "Vital Name": "", "Value": "", "Measurement Time": "" } ]
// }

// Transcript:
// ${combinedTranscription}
//   `;
// }



private buildUnifiedPrompt(combinedTranscription: string): string {
  return `
You are a clinical documentation assistant. 


Based on the provided transcript, extract all relevant information into a **single JSON object** containing:

1. Structured Medical Data:
- Prescriptions: columns Drug Name, Dose, SIG, Brand, Patient Notes. Fill missing details with "-". Mention Indian brands where applicable.
- Labs: explicitly ordered labs (Lab Name, Notes, Date Ordered) and suggested labs (Lab Name, Notes, Reason).
- Vitals: only vitals mentioned in the transcript using closest matching names. Include Measurement Time if mentioned; else "-".

2. Universal Notes:
- Array of concise, atomic clinical statements (strings) capturing facts, symptoms, findings, diagnoses, medications, instructions, or clinically relevant denials.
- Exclude filler or redundant details.
- Do not duplicate entries across chunks.
- Be precise and consistent.

3. Context Summary:
- Concise running summary (≤200 tokens) of the patient’s case so far.
- Keep it clinical, neutral, and without repetition.

**Important Instructions:**
- Return a single JSON object with exactly these fields: Prescriptions, Labs, Suggested Labs, Vitals, Universal_Notes, Context_Summary.
- Do not include markdown, backticks, or explanations—return pure JSON only.
- Do not omit any items present in the transcript.

Format:
{
  "Prescriptions": [ { "Drug Name": "", "Dose": "", "SIG": "", "Brand": "", "Patient Notes": "" } ],
  "Labs": [ { "Lab Name": "", "Notes": "", "Date Ordered": "" } ],
  "Suggested Labs": [ { "Lab Name": "", "Notes": "", "Reason": "" } ],
  "Vitals": [ { "Vital Name": "", "Value": "", "Measurement Time": "" } ],
  "Universal_Notes": [ "" ],
  "Context_Summary": ""
}

Transcript:
${combinedTranscription}
  `;
}


// private buildUnifiedPrompt(combinedTranscription: string): string {
//   return `
// You are a clinical documentation assistant.

// From the transcript below, extract **all possible clinically relevant information** into a single JSON object with exactly these fields:

// - "Prescriptions": [ { "Drug Name": "", "Dose": "", "SIG": "", "Brand": "", "Patient Notes": "" } ]
// - "Labs": [ { "Lab Name": "", "Notes": "", "Date Ordered": "" } ]
// - "Suggested Labs": [ { "Lab Name": "", "Notes": "", "Reason": "" } ]
// - "Vitals": [ { "Vital Name": "", "Value": "", "Measurement Time": "" } ]
// - "Universal_Notes": [ "" ]
// - "Context_Summary": ""

// ### Rules
// 1. **Always return valid JSON only** (no text, no markdown, no apologies).
// 2. **Never leave arrays empty if information can be reasonably inferred** from the transcript:
//    - If medications are discussed but details are incomplete → fill with "-" placeholders.
//    - If labs or vitals are implied but not fully specified → include them with "-" where missing.
// 3. "Universal_Notes" must capture every clinically relevant statement, symptom, finding, instruction, or denial, even if vague.
// 4. "Context_Summary" should be a concise neutral summary (≤200 tokens) of the patient’s case.
// 5. Use "-" for missing values inside objects.
// 6. Mention Indian brands where possible.

// Transcript:
// ${combinedTranscription}
//   `;
// }



}
