import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Patch,
  Post,
  Put,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { LabsService } from './labs.service';
import { Labs } from '@prisma/client';
import { ApiResponseDTO } from 'src/common/dtos';
import { CreateLabDto, LabDetailsDto, UpdateLabDto } from './dtos/labs.dto';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';
import { Request } from 'express';
@ApiTags('Labs')
@ApiBearerAuth('access-token')
@Controller('labs')
export class LabsController {
  constructor(private readonly labservice: LabsService) {}

  @Get('encounter/:encounterId')
  @PERMISSIONS('lab.read.self', 'lab.read.org')
  async getLabsByEncounterId(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
  ): Promise<ApiResponseDTO<Labs[]>> {
    return { data: await this.labservice.getLabsByEncounterId(encounterId) };
  }

  @Post()
  async createLab(
    @Body() data: CreateLabDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Labs[]>> {
    return { data: await this.labservice.createLab(data, userEmail) };
  }

  @Put('update-lab/:lab_id')
  @PERMISSIONS('lab.update.self', 'lab.update.org')
  async updateLab(
    @Param('lab_id', ParseUUIDPipe) lab_id: string,
    @Body() data: UpdateLabDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Labs>> {
    return { data: await this.labservice.updateLab(lab_id, data, userEmail) };
  }

  @Patch('delete-lab/:lab_id')
  @PERMISSIONS('lab.delete.self', 'lab.delete.org')
  async deleteLab(
    @Param('lab_id', ParseUUIDPipe) lab_id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Labs>> {
    return { data: await this.labservice.deleteLab(lab_id, userEmail) };
  }

  @Post('generate-labs/:encounter_id')
  @PERMISSIONS('lab.create.self', 'lab.create.org')
  async generateLabFromTranscription(
    @Param('encounter_id', ParseUUIDPipe) encounter_id: string,
    @GetUserEmail() userEmail: string,
    @Req() req: Request,
  ): Promise<ApiResponseDTO<any>> {
    return {
      data: await this.labservice.generateLabFromTranscription(
        encounter_id,
        userEmail,
        req
      ),
    };
  }

  @Patch(':lab_id/update-lab-status/:status')
  @PERMISSIONS('lab.update.self', 'lab.update.org')
  async updateLabStatusByLabId(
    @Param('lab_id', ParseUUIDPipe) lab_id: string,
    @Param('status') status: string,
    @GetUserEmail() userEmail: string,
    @Body() { status_reason }: { status_reason: string },
  ) {
    return await this.labservice.upadateLabStatusByLabid(
      lab_id,
      status,
      userEmail,
      status_reason,
    );
  }
}
