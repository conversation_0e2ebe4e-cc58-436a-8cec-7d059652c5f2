import { Module } from '@nestjs/common';
import { LabsController } from './labs.controller';
import { LabsRepository } from './labs.repository';
import { LabsService } from './labs.service';
import { TranscriptionsRepository } from 'src/transcriptions/transcriptions.repository';
import { TranscribeService } from 'aws-sdk';
import { TranscribeModule } from 'src/transcribe/transcribe.module';

@Module({
  imports: [TranscribeModule],
  controllers: [LabsController],
  exports : [LabsRepository],
  providers: [
    LabsRepository,
    LabsService,
    TranscriptionsRepository,
    TranscribeService,
  ],
})
export class LabsModule {}
