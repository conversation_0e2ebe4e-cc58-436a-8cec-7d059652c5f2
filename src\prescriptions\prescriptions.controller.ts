import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { ApiResponseDTO } from 'src/common/dtos';
import { PrescriptionsService } from './prescriptions.service';
import {
  CreatePrescriptionDto,
  UpdatePrescriptionDto,
} from './dtos/prescriptions.dto';
import { Prescriptions } from '@prisma/client';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';
import { Request } from 'express';

@ApiTags('prescriptions')
@ApiBearerAuth('access-token')
@Controller('prescriptions')
export class PrescriptionsController {
  constructor(private readonly prescriptionService: PrescriptionsService) {}

  @Get('generate-prescription/:encounterId')
  @PERMISSIONS('prescription.create.self', 'prescription.create.org')
  async genereatePrescriptionByEncounterId(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @GetUserEmail() userEmail: string,
    @Req() req: Request,
  ): Promise<ApiResponseDTO<any>> {
    return {
      data: await this.prescriptionService.generatePrescriptionsFromTranscriptions(
        encounterId,
        userEmail,
        req,
      ),
    };
  }

  @Post('create-prescription')
  @PERMISSIONS('prescription.create.self', 'prescription.create.org')
  async createPrescription(
    @Body() model: CreatePrescriptionDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Prescriptions[]>> {
    return {
      data: await this.prescriptionService.createPrescription(model, userEmail),
    };
  }

  @Put('update-prescription/:prescription_id')
  @PERMISSIONS('prescription.update.self', 'prescription.update.org')
  async updatePrescription(
    @Param('prescription_id', ParseUUIDPipe) prescription_id: string,
    @Body() model: UpdatePrescriptionDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Prescriptions>> {
    return {
      data: await this.prescriptionService.updatePrescription(
        model,
        prescription_id,
        userEmail,
      ),
    };
  }

  @Patch('delete-prescription/:prescription_id')
  @PERMISSIONS('prescription.delete.self', 'prescription.delete.org')
  async deletePrescription(
    @Param('prescription_id', ParseUUIDPipe) prescription_id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Prescriptions>> {
    return {
      data: await this.prescriptionService.deletePrescription(
        prescription_id,
        userEmail,
      ),
    };
  }
}
