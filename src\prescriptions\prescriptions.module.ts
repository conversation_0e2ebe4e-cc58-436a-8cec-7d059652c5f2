import { Module } from '@nestjs/common';
import { PrescriptionsController } from './prescriptions.controller';
import { PrescriptionsService } from './prescriptions.service';
import { PrescriptionsRepository } from './prescriptions.repository';
import { TranscriptionsModule } from 'src/transcriptions/transcriptions.module';
import { TranscriptionsRepository } from 'src/transcriptions/transcriptions.repository';

@Module({
  imports: [],
  exports: [PrescriptionsRepository],
  controllers: [PrescriptionsController],
  providers: [PrescriptionsService, PrescriptionsRepository,TranscriptionsRepository],
})
export class PrescriptionsModule {}
