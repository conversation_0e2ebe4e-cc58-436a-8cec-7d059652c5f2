import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { tbl_role_permission_mapping } from '@prisma/client';
import { CreateRolePermissionMappingDto } from './dtos/create-role-permission-mapping.dto';
import { PaginatedResultDTO } from 'src/common/dtos';

@Injectable()
export class RolePermissionRepository {
  private readonly logger = new Logger(RolePermissionRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) { }


  async createMapping(
    dto: CreateRolePermissionMappingDto,
    userEmail: string,
  ): Promise<tbl_role_permission_mapping> {
    const existing = await this.prisma.tbl_role_permission_mapping.findFirst({
      where: { role_id: dto.role_id, permission_id: dto.permission_id },
    });

    if (existing) {
      if (!existing.isActive) {
        return this.prisma.tbl_role_permission_mapping.update({
          where: { role_permission_id: existing.role_permission_id },
          data: { isActive: true, updated_by: userEmail },
        });
      }
      throw new Error('Mapping already exists and is active');
    }

    return this.prisma.tbl_role_permission_mapping.create({
      data: {
        role_id: dto.role_id,
        permission_id: dto.permission_id,
        type: dto.type ?? 'SYSTEM_DEFINED',
        created_by: userEmail,
      },
    });
  }

  async findMappings(
    role_id?: string,
    permission_id?: string,
    page = 1,
    limit = 10,
  ): Promise<PaginatedResultDTO<tbl_role_permission_mapping>> {
    const where: any = { isActive: true };
    if (role_id) where.role_id = role_id;
    if (permission_id) where.permission_id = permission_id;

    const [items, total] = await this.prisma.$transaction([
      this.prisma.tbl_role_permission_mapping.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { created_at: 'desc' },
      }),
      this.prisma.tbl_role_permission_mapping.count({ where }),
    ]);

    return {
      data: items,
      pagination: this.utilsService.getPagination(page, limit, total),
    };
  }

  async findMappingById(id: string): Promise<tbl_role_permission_mapping> {
    const mapping = await this.prisma.tbl_role_permission_mapping.findUnique({
      where: { role_permission_id: id },
    });
    if (!mapping) throw new NotFoundException('Mapping not found');
    return mapping;
  }

  async updateMapping(
    id: string,
    dto: Partial<CreateRolePermissionMappingDto>,
    userEmail: string,
  ): Promise<tbl_role_permission_mapping> {
    return await this.prisma.tbl_role_permission_mapping.update({
      where: { role_permission_id: id },
      data: {
        ...dto,
        updated_by: userEmail,
        updated_at: new Date(),
      },
    });
  }

  async deleteMapping(
    id: string,
    userEmail: string,
  ): Promise<tbl_role_permission_mapping> {
    return await this.prisma.tbl_role_permission_mapping.update({
      where: { role_permission_id: id },
      data: {
        isActive: false,
        updated_by: userEmail,
        updated_at: new Date(),
      },
    });
  }

  async getRolesPermissions() {
    const mappings = await this.prisma.tbl_role_permission_mapping.findMany({
      where:{
        isActive:true,
      },
      include: {
        tbl_role_lk: true,
        tbl_permission_lk: true
      }
    })
    // Group by role_name
    const grouped = mappings.reduce((acc, mapping) => {
      const roleName = mapping.tbl_role_lk.role_name;

      if (!acc[roleName]) {
        acc[roleName] = {
          role_id: mapping.role_id,
          role_name: mapping.tbl_role_lk.role_name,
          role_code: mapping.tbl_role_lk.role_code,
          permissions: [],
        };
      }

      acc[roleName].permissions.push({
        role_permission_id: mapping.role_permission_id,
        permission_id: mapping.permission_id,
        key: mapping.tbl_permission_lk.key,
        description: mapping.tbl_permission_lk.description,
        type: mapping.type,
        isActive: mapping.isActive,
      });

      return acc;
    }, {} as Record<string, any>);

    // Return as array instead of object (optional)
    return Object.values(grouped);
  }
}

