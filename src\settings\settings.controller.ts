import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { SettingsService } from './settings.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { CreateSettingDto, UpdateSettingDto } from './dto/settings.dto';
import { SettingTypes } from '@prisma/client';

@Controller('org-settings')
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Get()
  async getSettingByConfigType(@Query('config_type') config_type: SettingTypes) {
    return await this.settingsService.getSettingByConfigType(config_type);
  }

  @Put()
  async updateSetting(
    @Body() data: UpdateSettingDto,
    @GetUserEmail() userEmail: string,
  ) {
    if (!data.key) {
      throw new HttpException('Key is required', HttpStatus.BAD_REQUEST);
    }
    if (!data.value) {
      throw new HttpException('Value is required', HttpStatus.BAD_REQUEST);
    }
    return await this.settingsService.updateSetting(data, userEmail);
  }

  @Post()
  async createSetting(
    @Body() data: CreateSettingDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.settingsService.createSetting(data, userEmail);
  }
}
