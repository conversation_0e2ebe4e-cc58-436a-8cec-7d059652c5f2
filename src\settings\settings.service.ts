import { Injectable, NotFoundException } from '@nestjs/common';
import { SettingsRepository } from './settings.repository';
import { CreateSettingDto, UpdateSettingDto } from './dto/settings.dto';
import { SettingTypes } from '@prisma/client';

@Injectable()
export class SettingsService {
  constructor(private readonly settingsRepository: SettingsRepository) {}
  async getSettingByConfigType(config_type: SettingTypes) {
    return await this.settingsRepository.getSettingByConfigType(config_type);
  }

  async updateSetting(data: Partial<UpdateSettingDto>, userEmail: string) {
    const existingSetting = await this.settingsRepository.getSettingByKey(
      data.key,
    );
    if (!existingSetting) {
      throw new NotFoundException('Setting key not found');
    }
    return await this.settingsRepository.updateSetting(data, userEmail);
  }

  async createSetting(data: CreateSettingDto, userEmail: string) {
    return await this.settingsRepository.createSetting(data, userEmail);
  }
}
