import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import * as fs from 'fs';
import axios from 'axios';
import { PrismaClient } from '@prisma/client';
import { TranscribeRepository } from './transcribe.repository';
import {
  GoogleSpeechService,
  WhisperService,
} from 'src/common/utils/utils.service';
import { Request } from 'express';

@Injectable()
export class TranscribeService {
  private readonly logger = new Logger(TranscribeService.name);
  private readonly prisma = new PrismaClient();

  constructor(
    private readonly transcribeRepository: TranscribeRepository,
    private readonly whisperservice: WhisperService,
    private readonly googleSpeechService: GoogleSpeechService,
  ) {
    (BigInt.prototype as any).toJSON = function () {
      return this.toString();
    };
  }

  public async refineTranscription(
    text: string,
    aiModel: string = 'gpt-4o',
  ): Promise<string> {
    this.logger.log('Refining transcription...');
    try {
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: aiModel,
          messages: [
            {
              role: 'user',
              content: `Refine this transcript by discerning who the doctor and the patient are. Also be careful not to omit any content and try to correct the inaccuracies in the transcript due to poor audio or multi-lingual transcription mistakes.Also make sure to capture the patient and doctor names if given and replace the patient and doctor typo with name. Give the final output in english only: ${text}`,
            },
          ],
          max_tokens: 1200,
          temperature: 0.7,
        },
        {
          headers: {
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
          },
        },
      );
      this.logger.log('Transcription refined successfully.');
      return response.data.choices[0].message.content;
    } catch (error) {
      this.logger.error('Error refining transcription:', error.message);
      throw new Error(`Failed to refine transcription: ${error.message}`);
    }
  }

  async recognizeSpeech(
    req: Request,
    file: {
      originalname?: string;
      mimetype?: string;
      s3Uri?: string;
      buffer?: Buffer;
      path?: string;
      size?: number;
      patient_id?: string;
      encounter_id?: string;
      user_email?: string;
      duration?: string;
      type?: string;
    },
  ): Promise<{
    transcription: string;
    detectedLanguage: string;
    audioRecord: any;
  }> {
    let audioRecord;
    let audioBuffer: Buffer;

    try {
      // Create initial database record
      audioRecord =
        await this.transcribeRepository.createAudioTranscription(file);

      // Get audio buffer from various possible sources
      if (file.s3Uri) {
        try {
          const response = await axios({
            method: 'get',
            url: file.s3Uri,
            responseType: 'arraybuffer',
          });
          audioBuffer = Buffer.from(response.data);
        } catch (downloadError) {
          await this.handleError(
            audioRecord.id,
            `Download failed for audio file: ${file.s3Uri}`,
            downloadError,
            file.user_email,
          );
          throw new Error('Could not download audio file');
        }
      } else if (file.buffer) {
        audioBuffer = file.buffer;
      } else if (file.path) {
        audioBuffer = fs.readFileSync(file.path);
      } else {
        await this.handleError(
          audioRecord.id,
          'No valid audio source found in the request',
          null,
          file.user_email,
        );
        throw new Error('No valid audio source found');
      }

      let speechToTextResponse;
      try {
        // Make API request to Whisper uncomment this one when try to use whisper service
        // speechToTextResponse = await this.whisperservice.recognizeSpeechByWhisper(
        //   file.originalname,
        //   file.mimetype,
        //   audioBuffer,
        //   `Transcribe the audio, which includes english,tamil languages. Please identify and accurately transcribe each language segment, and ensure proper handling of accents, slang, and different dialects. Make sure to capture any nuances such as pauses, emphasis, or tone changes that contribute to the meaning of the conversation`,
        // );

        // this.logger.debug('Whisper API Response:', speechToTextResponse.data);

        // Make API request to Google Speech gcp
        // speechToTextResponse =
        //   await this.googleSpeechService.recognizeSpeechByGoogle(
        //     file.originalname,
        //     file.mimetype,
        //     audioBuffer,
        //   );

        // Make API request to Google Speech gcp chirp2

        const gcsuri = await this.googleSpeechService.uploadToGCS(
          process.env.GOOGLE_BUCKET_NAME,
          file.originalname,
          audioBuffer,
          file.mimetype,
        );

        speechToTextResponse =
          await this.googleSpeechService.googleChirp2Transcription(gcsuri, [
            'ta-IN',
            'en-US',
            'hi-IN',
            'en-IN',
          ]);
        this.logger.debug(
          'Google Speech API Response:',
          speechToTextResponse.data,
        );
      } catch (error) {
        const updatedAudioRecord =
          await this.transcribeRepository.updateAudioTranscription(
            audioRecord.id,
            null,
            null,
            file.user_email,
            null,
            '0',
          );

        this.logger.error(
          `Error generating transcription for audio file: ${file.originalname || 'unknown'}. Error: ${error.message}`,
          error?.stack,
        );
        throw new Error('we failed to generate the transcript');
      }
      // Make API request to Whisper

      // Extract data from the JSON response
      let transcription;
      const raw_transcription = speechToTextResponse.transcription || '';
      const detectedLanguage = speechToTextResponse.detectedLanguage || '';

      if (!raw_transcription) {
        this.logger.error(
          'Empty transcription received from speech recognization API',
        );
        throw new Error(
          'No transcription received from speech recognization API',
        );
      }

      // Refine the transcription
      try {
        const aiModel =
          (req.user as any).settings?.['default.ai.model'] ?? 'gpt-4o';
        transcription = await this.refineTranscription(
          raw_transcription,
          aiModel,
        );
      } catch (refinementError) {
        this.logger.warn(
          `Failed to refine transcription, using original: ${refinementError.message}`,
        );
        // Continue with original transcription if refinement fails
        transcription = raw_transcription;
      }

      // Update database with transcription results
      const updatedAudioRecord =
        await this.transcribeRepository.updateAudioTranscription(
          audioRecord.id,
          transcription,
          detectedLanguage,
          file.user_email,
          raw_transcription,
        );

      return {
        transcription,
        detectedLanguage,
        audioRecord: updatedAudioRecord,
      };
    } catch (error) {
      this.logger.error(
        `Error in speech recognition process for encounter ID: ${file.encounter_id || 'unknown'}`,
        error,
      );
      if (error.response) {
        // The server responded with a status other than 2xx
        this.logger.error(
          `speech recognization API error status: ${error.response.status}`,
        );
        this.logger.error('Error response headers:', error.response.headers);
        this.logger.error('Error response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        this.logger.error('No response received from speech recognization API');
      } else {
        // Something else happened while setting up the request
        this.logger.error(
          `Error setting up speech recognization API request: ${error.message}`,
        );
      }
      await this.handleError(
        audioRecord?.id,
        `Transcription failed for file: ${file.originalname || 'unknown'}`,
        error,
        file.user_email,
      );
      throw error;
    }
  }

  private async handleError(
    recordId: string | undefined,
    message: string,
    error: any,
    userEmail?: string,
  ) {
    const errorMessage = `${message}: ${error?.message || error || 'Unknown error'}`;
    this.logger.error(errorMessage);

    if (recordId) {
      try {
        await this.prisma.audioTranscription.update({
          where: { id: recordId },
          data: {
            is_processed: false,
            processing_error: errorMessage,
            updated_by: userEmail || 'SYSTEM',
          },
        });
      } catch (updateError) {
        this.logger.error(
          'Failed to update error status in database:',
          updateError,
        );
      }
    }

    throw new HttpException(
      {
        message: message, // User-friendly message
        error: error?.stack || JSON.stringify(error), // Detailed error information
        data: null,
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  async getTranscriptionTypeCount(
    encounterId: string,
    transcription_type: string,
  ): Promise<number> {
    try {
      return await this.transcribeRepository.getTrancriptionTypeCount(
        encounterId,
        transcription_type,
      );
    } catch (error) {
      this.logger.error(
        'Error getting transcription type count:',
        error?.stack,
      );
      throw error;
    }
  }
}
