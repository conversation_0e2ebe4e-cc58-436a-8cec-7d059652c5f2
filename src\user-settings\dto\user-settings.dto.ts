import { ApiProperty } from '@nestjs/swagger';
import { SettingTypes } from '@prisma/client';
import { JsonObject } from '@prisma/client/runtime/library';
import {
  IsEmail,
  IsEnum,
  IsJSON,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateUserSettingDto {
  @ApiProperty({
    description: 'Setting type',
    example: 'note.default_type',
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(SettingTypes)
  setting_type: SettingTypes;

  @ApiProperty({
    description: 'Setting value',
    example: 'SOAP Note',
  })
  @IsNotEmpty()
  setting_value: JsonObject;
}
