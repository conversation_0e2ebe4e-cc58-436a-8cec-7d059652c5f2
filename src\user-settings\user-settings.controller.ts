import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseEnumPipe,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { UserSettingsService } from './user-settings.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { CreateUserSettingDto } from './dto/user-settings.dto';
import { SettingTypes } from '@prisma/client';
import { Request } from 'express';
import { AiConfig } from 'src/common/config/common.config';

@Controller('user-settings')
export class UserSettingsController {
  constructor(private readonly userSettingsService: UserSettingsService) {}

  @Get()
  async getUserSetting(
    @GetUserEmail() userEmail: string,
    @Query('setting_type', new ParseEnumPipe(SettingTypes))
    settingType: SettingTypes,
  ) {
    if (!settingType) {
      throw new BadRequestException('Setting type is required');
    }

    return await this.userSettingsService.getUserSetting(
      userEmail,
      settingType,
    );
  }
  @Post()
  async createUserSetting(
    @Body() data: CreateUserSettingDto,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.userSettingsService.createUserSetting(
      userEmail,
      data.setting_type,
      data.setting_value,
    );
  }

  @Delete('reset')
  async resetUserSetting(
    @GetUserEmail() userEmail: string,
    @Query('setting_type', new ParseEnumPipe(SettingTypes))
    settingType: SettingTypes,
  ) {
    return await this.userSettingsService.resetUserSetting(userEmail, settingType);
  }
}
