import { Module } from '@nestjs/common';
import { UserSettingsService } from './user-settings.service';
import { UserSettingsController } from './user-settings.controller';
import { UserSettingsRepository } from './user-settings.repository';
import { SettingsRepository } from 'src/settings/settings.repository';

@Module({
  controllers: [UserSettingsController],
  providers: [UserSettingsService, UserSettingsRepository, SettingsRepository],
  exports: [UserSettingsService, UserSettingsRepository],
})
export class UserSettingsModule {}
