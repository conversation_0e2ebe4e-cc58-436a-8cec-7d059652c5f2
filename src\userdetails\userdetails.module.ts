import { Module } from '@nestjs/common';
import { UserdetailsService } from './userdetails.service';
import { UserdetailsController } from './userdetails.controller';
import { UserdetailsRepository } from './userdetails.repository';
import { Auth0Service } from 'src/auth/auth0/auth0.service';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
import { ConfigService } from '@nestjs/config';
@Module({
  providers: [UserdetailsService, UserdetailsRepository,Auth0Service, ConfigService,
      s3FileUploadService,],
  controllers: [UserdetailsController]
})
export class UserdetailsModule {}
