import { Injectable, Logger, NotFoundException, HttpStatus, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { tbl_user_details } from '@prisma/client';  // Prisma generates this type
import { CreateUserDetailsDto, UpdateProfileDto } from './dtos/CreateUserDetails.dto';
import {
  PaginatedResultDTO,
} from 'src/common/dtos';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';
@Injectable()
export class UserdetailsRepository {
  private readonly logger = new Logger(UserdetailsRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
    private readonly s3FileUploadService: s3FileUploadService,
  ) { }

  async createUser(data: CreateUserDetailsDto, userEmail: string): Promise<tbl_user_details> {
    try {
      return await this.prisma.tbl_user_details.create({
        data: {
          user_name: data.user_name,
          first_name: data.first_name,
          last_name: data.last_name,
          email: data.email,
          phone_number: data.phone_number,
          password: data.password,
          login_id: data.login_id,
          profile_picture: data.profile_picture,
          created_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error creating user:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }



  async findUsers(email?: string, user_name?: string, userEmail?: string, page = 1, limit = 10): Promise<PaginatedResultDTO<tbl_user_details>> {
    const where: any = {
      isActive: true,
      OR: [],
    };

    if (email) {
      where.OR.push({ email: { contains: email, mode: 'insensitive' } });
    }
    if (user_name) {
      where.OR.push({ user_name: { contains: user_name, mode: 'insensitive' } });
    }
    if (where.OR.length === 0) delete where.OR;

    const [items, total] = await this.prisma.$transaction([
      this.prisma.tbl_user_details.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { created_at: 'desc' },
      }),
      this.prisma.tbl_user_details.count({ where }),
    ]);

    return {
      data: items,
      pagination: this.utilsService.getPagination(page, limit, total),
    };
  }

  async getUserById(user_id: string): Promise<tbl_user_details> {
    try {
      const user = await this.prisma.tbl_user_details.findUnique({
        where: { user_id },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      this.logger.error('Error fetching user by ID:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch user by ID',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getUserByTenantId(tenantId: string, search: string, roleId: string) {
    console.log("RoleId", roleId)
    const users = await this.prisma.tbl_user_details.findMany({
      where: {
        tenant_id: tenantId,
        OR: search?.trim()
          ? [
            {
              first_name: { contains: search, mode: 'insensitive' }
            },
            {
              last_name: { contains: search, mode: 'insensitive' }
            },
            {
              email: { contains: search, mode: 'insensitive' }
            }
          ]
          : undefined,
        tbl_user_role_mapping: roleId ? {
          some: {
            roleLookup: {
              role_id: { equals: roleId }
            }
          }
        } : undefined,
        isActive: true,

      },

      include: {
        tbl_user_role_mapping: {
          include: {
            roleLookup: true
          }
        },
      },
      orderBy: {
        created_at: 'desc'
      }
    });
    return users
  }

  async updateUser(user_id: string, data: Partial<CreateUserDetailsDto>, userEmail: string): Promise<tbl_user_details> {
    try {
      return await this.prisma.tbl_user_details.update({
        where: { user_id },
        data: {
          ...data,
          updated_by: userEmail,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error('Error updating user:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteUser(user_id: string, userEmail: string): Promise<tbl_user_details> {
    try {
      return await this.prisma.tbl_user_details.update({
        where: { user_id },
        data: {
          isActive: false,
          updated_by: userEmail,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error('Error deleting user:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

 async updateProfile(
  email: string,
  data: UpdateProfileDto,
  userEmail: string,
  profilePicture?: Express.Multer.File,
) {
  try {
    return await this.prisma.$transaction(async (tx) => {
      // 🔹 Find existing user by email
      const existingUser = await tx.tbl_user_details.findFirst({
        where: { email },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      let imageUrl: string | undefined;

      // 🔹 Handle profile picture upload
      if (profilePicture) {
        if (existingUser.profile_picture) {
          await this.s3FileUploadService.deleteFileFromS3(existingUser.profile_picture);
        }
        const filePath = `users/${existingUser.user_id}/${Date.now()}-${profilePicture.originalname}`;
        imageUrl = await this.s3FileUploadService.uploadFileToS3(profilePicture, filePath);
      }

      // 🔹 Update tbl_user_details
     const updatedUser = await tx.tbl_user_details.update({
  where: { user_id: existingUser.user_id }, // ✅ use PK instead of email
  data: {
    phone_number: data.phone_number,
    ...(imageUrl && { profile_picture: imageUrl }),
    updated_by: userEmail,
    updated_at: new Date(),
  },
});

      // 🔹 Update Doctor if linked
      await tx.doctor.updateMany({
        where: { user_id: existingUser.user_id },
        data: {
          email: data.email,
          phone: data.phone_number,
          updated_by: userEmail,
          updated_at: new Date(),
        },
      });

      // 🔹 Update Contact if linked
      await tx.contact.updateMany({
        where: { contact_id: updatedUser.contact_id },
        data: {
          first_name: data.full_name?.split(' ')[0] ?? undefined,
          last_name: data.full_name?.split(' ')[1] ?? undefined,
          email: data.email,
          phone: data.phone_number,
          updated_by: userEmail,
          updated_at: new Date(),
        },
      });

      // 🔹 Update Address if provided
      if (data.street || data.city || data.zip || data.country) {
        await tx.address.updateMany({
          where: { contact_id: updatedUser.contact_id },
          data: {
            street: data.street,
            city: data.city,
            zip: data.zip,
            country: data.country,
            updated_by: userEmail,
            updated_at: new Date(),
          },
        });
      }

      return updatedUser;
    });
  } catch (error) {
    this.logger.error('Error updating profile:', error?.stack);
    throw this.utilsService.formatErrorResponse(
      error,
      'Failed to update profile',
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}



  async createUserByEmail(email: string) {
    const user = await this.prisma.tbl_user_details.create({
      data: {
        email: email,
      }
    });    

    console.log("Log Added",user)
    return { user_id: user.user_id, email: user.email }
  }

  async getProfile(email: string) {
    try {
      if (!email || !email.trim()) {
        throw new BadRequestException('Email must be provided');
      }
      const emailTrimmed = email.trim();
      const user = await this.prisma.tbl_user_details.findFirst({
        where: {
          email: { equals: emailTrimmed, mode: 'insensitive' }, // ✅ case-insensitive
        },
        include: {
          contact: { include: { addresses: true } },
          doctor: { include: { contact: { include: { addresses: true } } } },
        },
      });

      if (!user) {
        throw new NotFoundException(`User with email ${emailTrimmed} not found`);
      }

      return {
        user_id: user.user_id,
        user_name: user.user_name,
        email: user.email,
        phone_number: user.phone_number,
        profile_picture: user.profile_picture,
        contact: user.contact
          ? {
            contact_id: user.contact.contact_id,
            first_name: user.contact.first_name,
            last_name: user.contact.last_name,
            email: user.contact.email,
            phone: user.contact.phone,
            addresses: user.contact.addresses,
          }
          : null,
        doctor: user.doctor
          ? {
            doctor_id: user.doctor.doctor_id,
            email: user.doctor.email,
            phone: user.doctor.phone,
            first_name: user.doctor.first_name,
            last_name: user.doctor.last_name,
            specialty: user.doctor.specialty,
            contact: user.doctor.contact
              ? {
                contact_id: user.doctor.contact.contact_id,
                first_name: user.doctor.contact.first_name,
                last_name: user.doctor.contact.last_name,
                email: user.doctor.contact.email,
                phone: user.doctor.contact.phone,
                addresses: user.doctor.contact.addresses,
              }
              : null,
          }
          : null,
      };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error; // rethrow known exceptions
      }

      // Log error for debugging
      console.error('Error in getProfile:', error);

      throw new InternalServerErrorException('Failed to fetch user profile');
    }
  }
}
