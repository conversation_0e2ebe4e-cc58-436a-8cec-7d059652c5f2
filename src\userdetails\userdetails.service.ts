import { Injectable } from '@nestjs/common';
import { UserdetailsRepository } from './userdetails.repository';
import { CreateUserDetailsDto, UpdateProfileDto } from './dtos/CreateUserDetails.dto';
import { tbl_user_details } from '@prisma/client';
import {
  PaginatedResultDTO,

} from 'src/common/dtos';
import { UserManagementDto } from './dtos/user-management.dto';
import { Auth0Service } from 'src/auth/auth0/auth0.service';
@Injectable()
export class UserdetailsService {
  constructor(private readonly userRepo: UserdetailsRepository,
    private auth0Service: Auth0Service,
  ) { }

  async createUser(model: CreateUserDetailsDto, userEmail: string): Promise<tbl_user_details> {
    return await this.userRepo.createUser(model, userEmail);
  }

  async findUsers(
    email: string,
    user_name: string,
    userEmail: string,
    page = 1,
    limit = 10,
  ): Promise<PaginatedResultDTO<tbl_user_details>> {
    return await this.userRepo.findUsers(email, user_name, userEmail, page, limit);
  }

  async findOne(userId: string, userEmail: string): Promise<tbl_user_details> {
    return await this.userRepo.getUserById(userId);
  }

  async getUsersByTenantId(tenantId: string, input: UserManagementDto) {
    return await this.userRepo.getUserByTenantId(tenantId, input.search, input.roleId);
  }

  async inviteUser(email: string) {
    await this.auth0Service.inviteUser(email);
    return await this.userRepo.createUserByEmail(email)
  }

  async updateUser(
    userId: string,
    model: Partial<CreateUserDetailsDto>,
    userEmail: string,
  ): Promise<tbl_user_details> {
    return await this.userRepo.updateUser(userId, model, userEmail);
  }

  async deleteUser(userId: string, userEmail: string): Promise<tbl_user_details> {
    return await this.userRepo.deleteUser(userId, userEmail);
  }

async updateProfile(
  email: string,
  model: UpdateProfileDto,
  userEmail: string,
  profilePicture?: Express.Multer.File,
) {
  return await this.userRepo.updateProfile(email, model, userEmail, profilePicture);
}


async getProfile(email: string) {
  return await this.userRepo.getProfile(email);
}
}
