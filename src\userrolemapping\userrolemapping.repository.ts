import { Injectable, Logger, NotFoundException, HttpStatus } from '@nestjs/common';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';
import { tbl_user_role_mapping } from '@prisma/client';
import { CreateUserRoleMappingDto } from './dtos/dtos/create-user-role-mapping.dto';
import e from 'express';

@Injectable()
export class UserrolemappingRepository {
  private readonly logger = new Logger(UserrolemappingRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) { }

  async create(dto: CreateUserRoleMappingDto, userEmail: string): Promise<tbl_user_role_mapping> {
    try {
      return await this.prisma.tbl_user_role_mapping.create({
        data: {
          user_id: dto.user_id,
          role_lk_id: dto.role_lk_id,
          tenant_id: dto.tenant_id,
          created_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error creating mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string): Promise<tbl_user_role_mapping> {
    try {
      const mapping = await this.prisma.tbl_user_role_mapping.findUnique({
        where: { user_role_map_id: id },
        include: { roleLookup: true, tbl_user_details: true },
      });
      if (!mapping) {
        throw new NotFoundException('Mapping not found');
      }
      return mapping;
    } catch (error) {
      this.logger.error('Error fetching mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to fetch mapping',
        HttpStatus.NOT_FOUND,
      );
    }
  }


  async findAll(): Promise<tbl_user_role_mapping[]> {
    return await this.prisma.tbl_user_role_mapping.findMany({
      where: { isActive: true },
      include: {
        roleLookup: true,
        tbl_user_details: true,
      },
    });
  }

  async update(id: string, dto: Partial<CreateUserRoleMappingDto>, userEmail: string): Promise<tbl_user_role_mapping> {
    try {
      return await this.prisma.tbl_user_role_mapping.update({
        where: { user_role_map_id: id },
        data: {
          ...dto,
          updated_by: userEmail,
        },
        include: {
          roleLookup: true,
          tbl_user_details: true,
        },
      });
    } catch (error) {
      this.logger.error('Error updating mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateUserRole(userId: string, roleIds: string[], userEmail: string) {
    //check the user already exists
    const user = await this.prisma.tbl_user_details.findUnique({
      where: {
        user_id: userId,
        isActive: true
      }
    });
    if (!user) throw new NotFoundException('User not found');

    //Fetch all existing mappings (active and isactive)
    const existingMappings = await this.prisma.tbl_user_role_mapping.findMany({
      where: {
        user_id: userId
      }
    });

    const existingRoleIds = existingMappings.map(r => r.role_lk_id);
    const activeRoleIds = existingMappings.filter(r => r.isActive).map(r => r.role_lk_id);

    //roles to deactivate (soft delete)
    const rolesToDeactivate = activeRoleIds.filter(r => !roleIds.includes(r));
    if (rolesToDeactivate.length) {
      await this.prisma.tbl_user_role_mapping.updateMany({
        where: {
          user_id: userId,
          role_lk_id: { in: rolesToDeactivate },
          isActive: true
        },
        data: { isActive: false, updated_by: userEmail }
      })
    }

    //Roles to activate (already exist but inactive)
    const rolesToActivate = existingMappings.filter(r => !r.isActive && roleIds.includes(r.role_lk_id)).map(r => r.user_role_map_id);
    if (rolesToActivate.length) {
      await this.prisma.tbl_user_role_mapping.updateMany({
        where: {
          user_role_map_id: { in: rolesToActivate }
        },
        data: { isActive: true, updated_by: userEmail }
      })
    }

    //Roles to create(new mappings)
    const rolesToCreate = roleIds.filter(r => !existingRoleIds.includes(r));
    if (rolesToCreate.length) {
      const createdData = rolesToCreate.map(role_lk_id => ({
        user_id: userId,
        role_lk_id,
        created_by: userEmail
      }));
      await this.prisma.tbl_user_role_mapping.createMany({
        data: createdData
      })
    }
    return { message: 'User roles updated successfully' }
  }

  async remove(id: string, userEmail: string): Promise<tbl_user_role_mapping> {
    try {
      return await this.prisma.tbl_user_role_mapping.update({
        where: { user_role_map_id: id },
        data: {
          isActive: false,
          updated_by: userEmail,
        },
      });
    } catch (error) {
      this.logger.error('Error deleting mapping:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete mapping',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
