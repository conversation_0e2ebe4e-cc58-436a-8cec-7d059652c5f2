import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export class CreateVitalDto {
  @Type(() => VitalMeasurementDto)
  @ValidateNested({ each: true })
  info: VitalMeasurementDto[];
}

export class VitalMeasurementDto {
  @ApiProperty({
    description: 'The ID of the vital type',
    example: '6fd6be04-e7e3-448b-8dc3-dbc6636563a3',
  })
  @IsNotEmpty({ message: 'Vital type ID is required' })
  @IsUUID()
  vital_type_id: string;

  @ApiProperty({
    description: 'The ID of the encounter',
    example: 'dbb5c4a6-51a3-4e4a-8b58-d1b34c64c7d1',
  })
  @IsNotEmpty({ message: 'Encounter ID is required' })
  @IsUUID()
  encounter_id: string;

  @ApiProperty({
    description: 'The measured value of the vital',
    example: '120/80',
  })
  @IsOptional()
  @IsString()
  vital_value?: string;

  @ApiProperty({
    description: 'The time when the measurement was taken',
    example: '2025-09-01T10:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  measurement_time?: string;

  @ApiProperty({
    description: 'The ID of the vital measurement',
    example: 'dbb5c4a6-51a3-4e4a-8b58-d1b34c64c7d1',
  })
  @IsOptional()
  vital_measurement_id: string;
}

export class UpdateVitalDto extends PartialType(VitalMeasurementDto) { }
